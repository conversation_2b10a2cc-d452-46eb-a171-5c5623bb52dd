import { Button, Text } from "@kickavenue/ui/components"
import { Fragment } from "react"

import useSearchPreviewMore from "@hooks/useSearchPreviewMore"
import useSearchTopResult from "@hooks/useSearchTopResult"
import { getApiErrorMessage } from "@utils/network"
import { QueryStatus } from "types/network.type"
import { Product } from "types/product.type"
import Spinner from "@components/shared/Spinner"

import SearchStatus from "./SearchStatus"
import TopResultItem from "./TopResultItem"

const TopResults = () => {
  const { query } = useSearchTopResult()
  const { action, text } = useSearchPreviewMore(query)

  const data = query.data
  const error = getApiErrorMessage(query.error)
  if (error) {
    return <SearchStatus text="No results found" />
  }

  if (query?.isLoading || query?.status === QueryStatus.Pending) {
    return (
      <div className="mx-auto flex h-1/2 items-center justify-center">
        <Spinner />
      </div>
    )
  }

  return (
    <div className="mx-auto flex w-full flex-col justify-center gap-y-base xl:max-w-[calc(100vw-80px)]">
      <Text size="base" type="bold" state="primary">
        Top Results
      </Text>

      <div className="flex flex-col gap-y-sm">
        {data?.pages?.map((page) => (
          <Fragment key={page?.page}>
            {page?.products?.map((product: Product) => (
              <TopResultItem key={product.id} product={product} />
            ))}
          </Fragment>
        ))}
      </div>

      <Button
        size="lg"
        variant="secondary"
        className="mt-base !w-full"
        onClick={action}
      >
        {text}
      </Button>
    </div>
  )
}

export default TopResults
