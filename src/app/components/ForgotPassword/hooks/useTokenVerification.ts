import { useState, useEffect } from "react"

import { AuthUseCase } from "@application/usecases/AuthUseCase"
import { ConcreteAuthRepository } from "@infrastructure/repositories/ConcreteAuthRepository"
import useToast from "@app/hooks/useToast"

export const useTokenVerification = (
  token: string | null,
  emailParam: string | null,
) => {
  const [isTokenVerified, setIsTokenVerified] = useState(false)
  const [isError, setIsError] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { setShowToast } = useToast()

  useEffect(() => {
    if (token && emailParam) {
      verifyToken(token, emailParam)
    }
  }, [token, emailParam])

  const verifyToken = async (token: string, email: string) => {
    setIsLoading(true)

    const authRepository = new ConcreteAuthRepository()
    const authUseCase = new AuthUseCase(authRepository)

    try {
      await authUseCase.verifyResetToken(email, token)
      setIsTokenVerified(true)
    } catch (err) {
      if (err instanceof Error && err.message === "Token is invalid") {
        setIsError(true)
        setIsTokenVerified(false)
      }
      setShowToast(
        true,
        err instanceof Error
          ? err.message
          : "An error occurred while verifying the token",
        "danger",
      )
    } finally {
      setIsLoading(false)
    }
  }

  return { isTokenVerified, isLoading, isError }
}
