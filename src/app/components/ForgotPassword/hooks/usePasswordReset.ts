import { useState } from "react"
import { useRouter } from "next/navigation"

import { AuthUseCase } from "@application/usecases/AuthUseCase"
import { ConcreteAuthRepository } from "@infrastructure/repositories/ConcreteAuthRepository"
import useToast from "@app/hooks/useToast"

export const usePasswordReset = () => {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const authRepository = new ConcreteAuthRepository()
  const authUseCase = new AuthUseCase(authRepository)
  const { setShowToast } = useToast()

  const resetPassword = async (
    email: string,
    password: string,
    passwordConfirmation: string,
    token: string,
  ) => {
    setIsLoading(true)

    try {
      await authUseCase.resetPassword(
        email,
        password,
        passwordConfirmation,
        token,
      )
      setShowToast(true, "Password has been reset successfully", "success")
      setTimeout(() => {
        router.push("/login")
      }, 2000)
    } catch (err) {
      setShowToast(
        true,
        err instanceof Error ? err.message : "An error occurred",
        "danger",
      )
    } finally {
      setIsLoading(false)
    }
  }

  const requestPasswordReset = async (email: string) => {
    setIsLoading(true)

    try {
      await authUseCase.requestPasswordReset(email)
      setShowToast(
        true,
        "Password reset link has been sent to your email",
        "success",
      )
    } catch (err) {
      setShowToast(
        true,
        err instanceof Error ? err.message : "An error occurred",
        "danger",
      )
    } finally {
      setIsLoading(false)
    }
  }

  return {
    resetPassword,
    requestPasswordReset,
    isLoading,
    setShowToast,
  }
}
