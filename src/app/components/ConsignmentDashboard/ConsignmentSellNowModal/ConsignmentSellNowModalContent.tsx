import { But<PERSON>, Divider } from "@kickavenue/ui/dist/src/components"
import TTextProps from "@kickavenue/ui/dist/src/components/Text/Text.type"

import useFetchSellerListingById from "@app/hooks/useFetchSellerListingById"
import ModalFooter from "@components/shared/ModalParts/ModalFooter"
import ModalProduct from "@components/shared/ModalParts/ModalProduct"
import ModalSummaryTotal from "@components/shared/ModalParts/ModalSummaryTotal"
import ModalUniqueID from "@components/shared/ModalParts/ModalUniqueID"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import { useConsignmentModalStore } from "stores/consignmentModalStore"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import { useMemberStore } from "stores/memberStore"
import { formatPrice, getStripAmount } from "@utils/misc"
import {
  calculatePlatformFee,
  getPlatformFeePercentage,
} from "@utils/fee.utils"
import ModalSellingSummary from "@components/shared/ModalParts/ModalSellingSummary"
import ModalSummaryItem from "@components/shared/ModalParts/ModalSummaryItem"
import useGetPlatformFee from "@app/hooks/useGetPlatformFee"

const { CONSIGNMENT_SELL_NOW } = ModalConstant.MODAL_IDS

const ConsignmentSellNowModalContent = () => {
  const { sellerListingId } = useConsignmentModalStore()
  const { setOpen } = useModalStore()
  const { member } = useMemberStore()

  const { data: sellerListing, isLoading } = useFetchSellerListingById(
    sellerListingId as number,
  )

  const { getMyPlatformFee } = useGetPlatformFee({
    memberId: member?.id,
    categoryId: sellerListing?.item?.category?.id,
  })

  const offerAmount = getStripAmount(sellerListing?.highestOfferAmount) || 0
  const calcFee = calculatePlatformFee(offerAmount, getMyPlatformFee())

  const summaries = [
    {
      text: "Offer Price",
      value: formatPrice(offerAmount, null, "IDR"),
    },
    {
      text: `Platform Fee (${getPlatformFeePercentage(getMyPlatformFee())}%)`,
      value: formatPrice(calcFee, null, "IDR"),
      isMinus: true,
      valueProps: { state: "danger" } as Partial<TTextProps>,
    },
  ]

  if (isLoading) {
    return <SpinnerLoading className="h-[331px]" />
  }

  return (
    <>
      <div className="flex max-h-[331px] flex-col gap-lg overflow-y-auto p-lg">
        <div className="flex flex-col gap-sm">
          <div className="">
            <ModalProduct listing={sellerListing} />
          </div>
          <ModalUniqueID
            text="Consignment ID"
            value={sellerListing?.consignmentId || "-"}
            copiedMessage="Consignment ID copied!"
          />
        </div>
        <Divider orientation="horizontal" />
        <div className="">
          <ModalSellingSummary>
            {summaries.map((summary) => (
              <ModalSummaryItem key={summary.text} {...summary} />
            ))}
            <Divider orientation="horizontal" />
            <ModalSummaryTotal
              text="Total Revenue Sales"
              value={formatPrice(offerAmount - calcFee, null, "IDR")}
            />
          </ModalSellingSummary>
        </div>
      </div>
      <ModalFooter>
        <div className="col-span-12">
          <Button
            size="lg"
            variant="primary"
            disabled={false}
            className="!w-full"
            onClick={() => setOpen(false, CONSIGNMENT_SELL_NOW)}
          >
            Sell Now
          </Button>
        </div>
      </ModalFooter>
    </>
  )
}

export default ConsignmentSellNowModalContent
