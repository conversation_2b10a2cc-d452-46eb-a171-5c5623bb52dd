import { useEffect } from "react"
import { useInView } from "react-intersection-observer"

import SearchLoading from "@components/ExpandedSearch/SearchLoading"
import Spinner from "@components/shared/Spinner/Spinner"
import ProductList from "@shared/ProductList"
import { isHttpNotFoundError } from "@utils/network"
import { useMiscStore } from "stores/miscStore"
import { useSearchStore } from "stores/searchStore"

import NoProductFound from "./NoProductFound"

export interface SearchResultProps {
  fetchNextPage: () => void
  hasNextPage: boolean
  isFetchingNextPage: boolean
}

const SearchResult = ({
  fetchNextPage,
  hasNextPage,
  isFetchingNextPage,
}: SearchResultProps) => {
  const {
    products,
    isLoading,
    status,
    error,
    productWishlist,
    setProductWishlist,
  } = useSearchStore()
  const { ref, inView } = useInView()
  const { setShowWishlistSnackbar } = useMiscStore()

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage()
    }
  }, [inView, hasNextPage, fetchNextPage])

  if (isLoading || status === "pending") {
    return <SearchLoading />
  }

  if (isHttpNotFoundError(error) || !products?.length) {
    return <NoProductFound />
  }

  return (
    <div className="grid grid-cols-4 gap-lg md:grid-cols-9 lg:grid-cols-12">
      <ProductList
        list={products}
        containerProps={{ className: "col-span-2 md:col-span-3" }}
        productWishlist={productWishlist}
        onUnWishlisted={(_, product) => setProductWishlist(product.id, 0)}
        onWishlistAdded={(wishlistIds, product) => {
          setProductWishlist(product.id, wishlistIds?.[0] || 0)
          setShowWishlistSnackbar(true)
        }}
      />
      {hasNextPage && (
        <div className="col-span-2 md:col-span-3">
          <div ref={ref} className="py-sm">
            {isFetchingNextPage && <Spinner />}
          </div>
        </div>
      )}
    </div>
  )
}

export default SearchResult
