"use client"

import { useEffect } from "react"

import useSearchProduct from "@app/hooks/useSearchProduct"
import CenterWrapper from "@components/shared/CenterWrapper"
import { useMiscStore } from "stores/miscStore"

import FilterContent from "./FilterContent"
import FilterDrawer from "./FilterDrawer"
import SearchBreadCrumb from "./SearchBreadCrumb"
import SearchChips from "./SearchChips"
import SearchResult from "./SearchResult"
import SearchResultAndSort from "./SearchResultAndSort"

const SearchContainer = () => {
  const { setSearchKeyword } = useMiscStore()
  const { fetchNextPage, hasNextPage, isFetchingNextPage } = useSearchProduct()

  useEffect(() => {
    return () => {
      setSearchKeyword("")
    }
  }, [setSearchKeyword])

  return (
    <>
      <SearchBreadCrumb />
      <div className="mb-lg md:mb-xl" />
      <CenterWrapper className="!mb-xl md:!gap-lg md:!px-xxl md:!py-0">
        <div className="sticky top-[150px] hidden overflow-y-auto md:col-span-3 md:block">
          <FilterContent />
        </div>
        <div className="col-span-12 md:col-span-9">
          <SearchResultAndSort />
          <SearchChips />
          <div className="mb-base md:mb-xl" />
          <SearchResult
            fetchNextPage={fetchNextPage}
            hasNextPage={hasNextPage}
            isFetchingNextPage={isFetchingNextPage}
          />
        </div>
      </CenterWrapper>

      <FilterDrawer />
    </>
  )
}

export default SearchContainer
