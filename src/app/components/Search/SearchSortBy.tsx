import {
  DropdownItemProps,
  IconArrowDownOutline,
  Text,
} from "@kickavenue/ui/components"
import { useSearchParams } from "next/navigation"
import { useMemo } from "react"

import useURLQuery from "@app/hooks/useUrlQuery"
import DropdownDynamicChild from "@components/shared/Form/DropdownDynamicChild"
import { MiscConstant } from "@constants/misc"
import { TProductFilterKey } from "types/product.type"

const { SortBy } = TProductFilterKey
const { SORT_BY_OPTIONS } = MiscConstant

const SearchSortBy = () => {
  const { handleChangeQuery } = useURLQuery()
  const searchParams = useSearchParams()

  const selected = useMemo(() => {
    const sortBy = searchParams?.get(SortBy)
    if (!sortBy) {
      return SORT_BY_OPTIONS.NEWLY_ADDED
    }
    return SORT_BY_OPTIONS[sortBy as keyof typeof SORT_BY_OPTIONS]
  }, [searchParams])

  const handleItemSelect = (option: DropdownItemProps) => {
    handleChangeQuery(SortBy, option.value)
  }

  return (
    <DropdownDynamicChild
      options={Object.values(SORT_BY_OPTIONS)}
      placement="rightBottom"
      onItemSelect={handleItemSelect}
    >
      <Text size="base" type="bold" state="primary">
        <div className="flex items-center gap-xs">
          Sort By
          <span className="hidden md:inline">:</span>
          <span className="hidden md:inline">{selected?.text}</span>{" "}
          <IconArrowDownOutline />
        </div>
      </Text>
    </DropdownDynamicChild>
  )
}

export default SearchSortBy
