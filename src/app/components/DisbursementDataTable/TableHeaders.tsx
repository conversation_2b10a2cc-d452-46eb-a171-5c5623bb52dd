import { formatDateFull, formatPrice } from "@utils/misc"
import {
  EDisbursementActionNameType,
  EDisbursementStatusesType,
  EKickCreditDisbursementType,
  EKickPointDisbursementType,
  ESellerCreditDisbursementType,
  GetMyDisbursementData,
} from "types/disbursement.type"
import { TTableColumn } from "types/table.type"

import DisbursementTableColumnAction from "./TableColumnAction"

const renderDisbursementType = (value: GetMyDisbursementData) => {
  // @ts-expect-error ignore ts error
  const typeMap: Record<EDisbursementActionNameType, React.ReactNode> = {
    [EDisbursementActionNameType.TopUp]: (
      <div className="text-success">Top Up</div>
    ),
    [EDisbursementActionNameType.CashIn]: (
      <div className="text-success">Cash In</div>
    ),
    [EDisbursementActionNameType.BuyingReward]: (
      <div className="text-success">Buying Reward</div>
    ),
    [EDisbursementActionNameType.CashOut]: (
      <div className="text-danger">Cash Out</div>
    ),
  }

  return (
    typeMap[value.actionName] || <div className="text-danger">Purchase Out</div>
  )
}

const renderDisbursementStatus = (value: GetMyDisbursementData) => {
  const statusMap = {
    [EDisbursementStatusesType.Pending]: (
      <div className="text-warning">Pending</div>
    ),
    [EDisbursementStatusesType.Completed]: (
      <div className="text-success">Completed</div>
    ),
    [EDisbursementStatusesType.Failed]: (
      <div className="text-danger">Failed</div>
    ),
    [EDisbursementStatusesType.Canceled]: (
      <div className="text-danger">Canceled</div>
    ),
    [EDisbursementStatusesType.Unverified]: (
      <div className="text-danger">Unverified</div>
    ),
    [EDisbursementStatusesType.Rejected]: (
      <div className="text-danger">Rejected</div>
    ),
    [EDisbursementStatusesType.InProgress]: (
      <div className="text-accent">In Progress</div>
    ),
  }

  return statusMap[value.status] || <div className="text-danger">Expired</div>
}

export const disbursementTableHeaders = (
  disbursementType?:
    | EKickCreditDisbursementType
    | EKickPointDisbursementType
    | ESellerCreditDisbursementType,
): TTableColumn[] => {
  const isHasOrderId =
    disbursementType === EKickPointDisbursementType.KickPointLog ||
    disbursementType === ESellerCreditDisbursementType.SellerPendingCashOut ||
    disbursementType === ESellerCreditDisbursementType.SellerCreditLog

  const isKickPointLog =
    disbursementType === EKickPointDisbursementType.KickPointLog

  const baseColumns: TTableColumn[] = [
    {
      title: "Disbursement No.",
      dataIndex: "disbursementNumber",
      key: "disbursementNumber",
    },
    {
      title: "Type",
      dataIndex: "actionName",
      key: "actionName",
      render: (value: GetMyDisbursementData) => (
        <>{renderDisbursementType(value)}</>
      ),
    },
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      render: (value: GetMyDisbursementData) => (
        <>{formatDateFull(value.createdAt)}</>
      ),
    },
    {
      title: "Amount",
      dataIndex: "amount",
      key: "amount",
      headerClassName: "text-right [&>div]:!justify-end",
      contentClassName: "text-right",
      render: (value: GetMyDisbursementData) => (
        <>{formatPrice(value.amount, null, isKickPointLog ? "KP" : "IDR")}</>
      ),
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: renderDisbursementStatus,
    },
    {
      title: "Action",
      key: "actions",
      headerClassName: "text-center [&>div]:!justify-center",
      render: (value: GetMyDisbursementData) => (
        <DisbursementTableColumnAction
          data={value}
          disbursementType={disbursementType}
        />
      ),
    },
  ]

  // Insert Order ID column after Disbursement No. if needed
  if (isHasOrderId) {
    baseColumns.splice(1, 0, {
      title: "Order ID",
      dataIndex: "invoiceNumber",
      key: "invoiceNumber",
    })
  }

  return baseColumns
}
