"use client"

import styles from "@kickavenue/ui/components/Carousel/Carousel.module.css"
import CarouselItem from "@kickavenue/ui/components/Carousel/CarouselItem"
import { cx } from "class-variance-authority"
import Image from "next/image"

import { convertS3UrlToCloudFront } from "@utils/misc"
import { IMenuWizardSection } from "types/menuWizard"

interface RaffleCarouselProps {
  isSingleRaffle: boolean
  activeRaffles: IMenuWizardSection[]
  itemRef: React.RefObject<HTMLDivElement>
  handleNext: () => void
  handlePrev: () => void
  activeIndex: number
}

const RaffleCarousel = ({
  isSingleRaffle,
  activeRaffles,
  itemRef,
  handleNext,
  handlePrev,
  activeIndex,
}: RaffleCarouselProps) => {
  if (isSingleRaffle) {
    return (
      <div className="relative">
        <Image
          alt={activeRaffles[0].title || `Raffle ${activeRaffles[0].id}`}
          src={convertS3UrlToCloudFront(
            activeRaffles[0].sectionContent?.banner || "",
          )}
          width={1440}
          height={400}
          className="size-full max-h-[400px] object-cover"
          priority
        />
      </div>
    )
  }

  const totalItems = activeRaffles.length
  const maxIndex = totalItems - 1

  return (
    <div className="relative">
      <div
        data-variant="line"
        data-size="lg"
        className={cx(styles.carousel, "h-full max-h-[400px]")}
      >
        <CarouselItem itemRef={itemRef}>
          {activeRaffles.map((item) => (
            <div key={item.id} className="relative">
              <Image
                alt={item.title || `Raffle ${item.id}`}
                src={convertS3UrlToCloudFront(
                  item.sectionContent?.banner || "",
                )}
                width={1440}
                height={400}
                className="size-full rounded-t-lg object-cover"
                priority
              />
            </div>
          ))}
        </CarouselItem>
      </div>

      <div className="absolute inset-y-0 left-0 flex items-center">
        <button
          type="button"
          onClick={handlePrev}
          disabled={activeIndex === 0}
          className="shadow-lg ml-6 flex size-10 items-center justify-center rounded-full bg-white disabled:opacity-50"
        >
          <span className="sr-only">Previous</span>
          <svg
            className="size-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
      </div>

      <div className="absolute inset-y-0 right-0 flex items-center">
        <button
          type="button"
          onClick={handleNext}
          disabled={activeIndex === maxIndex}
          className="shadow-lg mr-6 flex size-10 items-center justify-center rounded-full bg-white disabled:opacity-50"
        >
          <span className="sr-only">Next</span>
          <svg
            className="size-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>
      </div>
    </div>
  )
}

export default RaffleCarousel
