.table-container {
  @apply relative h-[600px] w-full overflow-x-auto;
  .table {
    @apply w-[1400px];
    table-layout: fixed;
    thead {
      @apply sticky top-0 z-10;

      tr {
        td {
          @apply w-fit border-none bg-gray-w-95 p-sm text-sm font-bold text-gray-b-65;
        }
        td:first-child {
          @apply rounded-l-xs;
        }
        td:last-child {
          @apply rounded-r-xs;
        }
      }
    }
    td {
      &:nth-child(5) {
        @apply text-right;
        div {
          @apply justify-end;
        }
      }
    }
  }
}
@screen md {
  .sticky-header-1,
  .sticky-content-1 {
    @apply sticky left-0 z-10;
  }

  .sticky-header-2,
  .sticky-content-2 {
    @apply sticky left-[48px] z-10;
    &::after {
      content: "";
      @apply absolute bottom-0 right-0 top-0 w-[1px] bg-gray-w-95;
    }
    &::before {
      content: "";
      @apply pointer-events-none absolute -right-[5px] bottom-0 top-0 w-[5px];
      background: linear-gradient(
        to right,
        rgba(0, 0, 0, 0.1),
        rgba(0, 0, 0, 0)
      );
    }
  }

  .sticky-right-header-1,
  .sticky-right-content-1 {
    @apply sticky right-0 z-10;
    &::after {
      content: "";
      @apply absolute bottom-0 right-0 top-0 w-[1px] bg-gray-w-95;
    }
    &::before {
      content: "";
      @apply pointer-events-none absolute -left-[5px] bottom-0 top-0 w-[5px];
      background: linear-gradient(
        to left,
        rgba(0, 0, 0, 0.1),
        rgba(0, 0, 0, 0)
      );
    }
  }
}
.sticky-content-1,
.sticky-content-2,
.sticky-right-content-1 {
  @apply bg-white;
}
