import useBuyingOfferPagination from "@app/hooks/useBuyingOfferPagination"
import useFetchMyOffers from "@app/hooks/useFetchMyOffers"
import SellingTableEmpty from "@components/SellingDashboard/SellingTableEmpty"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import Table from "@components/shared/Table"
import TablePagination from "@components/shared/Table/TablePagination"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { removeDuplicates } from "@utils/array.utils"
import { buildSortByQueryString, camelToSnake, isEmpty } from "@utils/misc"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import { TSort } from "types/apiResponse.type"
import { TOffer, TOfferFilter } from "types/offer.type"
import { TSorter } from "types/table.type"

import useBuyingDashboardOfferTable from "../hooks/useBuyingDashboardOfferTable"

import styles from "./buyingDashboardOfferTable.module.scss"

const BuyingDashboardOfferTable = () => {
  const {
    buyingOfferData,
    buyingOfferFilter,
    selectedRowKeys,
    buyingOfferAllData,
    setBuyingOfferData,
    setBuyingOfferFilter,
    setSelectedRowKeys,
    setBuyingOfferAllData,
  } = useBuyingOfferStore()

  const { columns } = useBuyingDashboardOfferTable()

  const paging = useBuyingOfferPagination()

  const { isLoading } = useFetchMyOffers({
    onSuccess: (data) => {
      const newData = removeDuplicates(
        [...(data?.content || []), ...buyingOfferAllData],
        "id",
      )
      setBuyingOfferAllData(newData)
      setBuyingOfferData((data?.content || []) as TOffer[])
      setBuyingOfferFilter({
        ...((buyingOfferFilter || {}) as TOfferFilter),
        totalPages: data?.totalPages,
      })
    },
    filter: buyingOfferFilter as TOfferFilter,
    enabled:
      !isEmpty(buyingOfferFilter?.page) &&
      !isEmpty(buyingOfferFilter?.pageSize) &&
      !isEmpty(buyingOfferFilter?.status) &&
      !isEmpty(buyingOfferFilter?.sortBy),
  })

  const handleTableChange = (sorter: TSorter) => {
    const field = camelToSnake(sorter.field)?.toLowerCase()
    setBuyingOfferFilter({
      ...((buyingOfferFilter || {}) as TOfferFilter),
      sortBy: [{ sortBy: field, sortOrder: sorter.order }],
    })
  }

  if (isLoading) {
    return <SpinnerLoading className="min-h-[calc(100vh-400px)]" />
  }

  if (!isLoading && !buyingOfferData?.length) {
    return (
      <SellingTableEmpty
        title="Start Your Journey to Exclusive Deals!"
        subText="Your offer journey begins here. Explore our collection, make your first offer, and secure exclusive deals on items you love."
        pageLink={PageRouteConstant.SEARCH}
        btnText="Discover Deals Now"
      />
    )
  }

  return (
    <>
      <div className={styles["table-container"]}>
        <Table
          columns={columns}
          dataSource={buyingOfferData}
          rowKey="id"
          className={styles.table}
          rowSelection={{
            selectedRowKeys,
            onChange: (selected: number[]) => setSelectedRowKeys(selected),
          }}
          onTableChange={handleTableChange}
          loading={isLoading}
          activeSort={buildSortByQueryString(
            (buyingOfferFilter?.sortBy || []) as TSort[],
          )}
        />
      </div>

      <div className="flex justify-end gap-base">
        <TablePagination {...paging} />
      </div>
    </>
  )
}

export default BuyingDashboardOfferTable
