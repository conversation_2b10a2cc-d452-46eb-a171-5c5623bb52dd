import {
  Divider,
  IconLogoKickAvenue,
  Space,
  Text,
} from "@kickavenue/ui/dist/src/components"
import TDrawerMenuProps from "@kickavenue/ui/dist/src/components/DrawerMenu/DrawerMenu.type"
import { cx } from "class-variance-authority"
import { HTMLProps } from "react"

import ClickableDiv from "@components/shared/ClickableDiv"
import useBodyOverflow from "@app/hooks/useBodyOverflow"

import styles from "./DrawerMenu.module.css"
import DrawerMenuHeader from "./DrawerMenuHeader/DrawerMenuHeader"
import DrawerMenuLink from "./DrawerMenuLink"

const DrawerMenu = (props: TDrawerMenuProps) => {
  const {
    className,
    visible = false,
    toggleDrawerMenu,
    links,
    containerProps,
    linkActive,
    ...rest
  } = props
  const { ...restContainer } = (containerProps ??
    {}) as HTMLProps<HTMLDivElement>

  useBodyOverflow(visible)

  return (
    <div
      className={cx(styles.container, containerProps?.className)}
      data-visible={visible}
      {...restContainer}
    >
      <ClickableDiv
        onClick={toggleDrawerMenu}
        keyDownHandler={toggleDrawerMenu}
        className={styles.overlay}
      >
        <div />
      </ClickableDiv>
      <div className={cx(styles.drawerMenu, className)} {...rest}>
        <DrawerMenuHeader {...props} />
        <Space direction="y" size="lg" type="margin" />
        <Divider type="solid" orientation="horizontal" />
        <Space direction="y" size="lg" type="margin" />
        <Space direction="x" size="base" type="padding">
          <DrawerMenuLink links={links} linkActive={linkActive} />
        </Space>
        <div className={styles.logo}>
          <IconLogoKickAvenue />
          <Text size="sm" type="regular" state="primary">
            Download Kick Avenue App
          </Text>
        </div>
      </div>
    </div>
  )
}

export default DrawerMenu
