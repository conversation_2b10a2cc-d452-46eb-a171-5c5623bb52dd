.container {
  @apply absolute left-0 top-0 z-20 h-full min-h-screen w-full font-plus-jakarta;
  transition: all 0.3s ease-in-out;
  transform: translateX(-100%);
}

.container .overlay {
  @apply absolute left-0 top-0 h-full w-full bg-black-dim-40 opacity-0;
}

.container .drawerMenu {
  @apply absolute left-0 top-0 z-50 h-full w-[294px] bg-white;
}

.container[data-visible="true"] {
  transform: translate(0);
}

.container[data-visible="true"] .overlay {
  @apply opacity-[0.6];
  transition: all 0.5s ease-in-out;
}

.container .drawerMenu svg {
  @apply h-6 w-6;
}

.iconClose {
  @apply cursor-pointer;
}

.btn {
  @apply flex w-full justify-between gap-sm;

  & button {
    width: 100%;
  }
}

.profile {
  @apply flex items-center gap-sm;

  & img {
    @apply h-10 w-10 rounded-full bg-black object-cover;
  }
}

.link {
  @apply m-0 flex list-none flex-col gap-xl p-0 text-base font-normal text-gray-b-65;

  & li {
    @apply flex cursor-pointer items-center gap-sm;
  }

  & [data-is-active="true"] {
    @apply font-bold;
  }
  & a {
    @apply no-underline;
    color: inherit;
  }
}

[data-is-active="true"] {
  &.link {
    @apply !bg-red-b-30;
  }
}

.logo {
  @apply absolute bottom-0 left-0 right-0 flex items-center gap-x-sm bg-gray-w-95 p-base;
}
