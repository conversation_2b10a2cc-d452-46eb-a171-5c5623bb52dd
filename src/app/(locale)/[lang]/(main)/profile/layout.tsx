import { ReactNode } from "react"

import { SidebarProfile } from "@app/components/Profile/SidebarProfile"
import Wallet from "@app/components/Profile/Wallet"
import { AuthRedirectWrapper } from "@components/AuthRedirectWrapper"
import { PageRouteConstant } from "@constants/pageRoute.constant"

const ProfileLayout: React.FC<{ children: ReactNode }> = ({ children }) => {
  return (
    <AuthRedirectWrapper whenUnauthed={PageRouteConstant.LOGIN}>
      <div className="bg-gray-w-95 lg:flex lg:justify-center">
        <div className="flex w-full gap-lg lg:p-xl xl:max-w-[calc(100vw-80px)] xl:px-0">
          <div className="flex h-full w-[294px] shrink-0 flex-col gap-base">
            <Wallet />
            <SidebarProfile />
          </div>
          <div className="m-sm w-full max-w-[calc(100vw-374px)] rounded-base bg-white lg:m-0 lg:h-full">
            {children}
          </div>
        </div>
      </div>
    </AuthRedirectWrapper>
  )
}

export default ProfileLayout
