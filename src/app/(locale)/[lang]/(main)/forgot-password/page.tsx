"use client"

import { Head<PERSON>, But<PERSON> } from "@kickavenue/ui/components"

import EmailInput from "@app/components/InputEmail"
import { useForgotPasswordForm } from "@app/components/ForgotPassword/hooks/useForgotPasswordForm"
import { usePasswordReset } from "@app/components/ForgotPassword/hooks/usePasswordReset"

const ForgotPassword = () => {
  const { email, handleEmailChange } = useForgotPasswordForm()

  const { requestPasswordReset, isLoading } = usePasswordReset()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    await requestPasswordReset(email)
  }

  return (
    <div className="mx-auto mb-80 mt-8 max-w-md">
      <div className="mb-8 text-center">
        <Heading heading="4" textStyle="bold">
          Forgot Password
        </Heading>
      </div>
      <form onSubmit={handleSubmit}>
        <EmailInput
          onChange={handleEmailChange}
          value={email}
          className="mb-4"
        />
        <div className="mt-8 w-full">
          <Button
            size="lg"
            variant="primary"
            className="!w-full"
            type="submit"
            disabled={isLoading}
          >
            Send Reset Link
          </Button>
        </div>
      </form>
    </div>
  )
}

export default ForgotPassword
