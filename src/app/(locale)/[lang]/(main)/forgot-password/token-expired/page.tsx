"use client"

import React from "react"
import {
  But<PERSON>,
  Head<PERSON>,
  IconDangerCircleBulk,
  Text,
} from "@kickavenue/ui/dist/src/components"
import { useRouter } from "next/navigation"

export default function ErrorPage() {
  const router = useRouter()

  const handleTryAgain = () => {
    router.push("/forgot-password")
  }

  return (
    <div className="flex h-svh flex-col items-center justify-center gap-lg bg-white py-xxl">
      <IconDangerCircleBulk className="size-[29px] scale-[2.2] text-danger" />
      <Heading heading="4" textStyle="bold">
        Oops, Time&apos;s Up
      </Heading>
      <Text size="base" state="secondary" type="regular">
        That link&apos;s expired. No problem--grab a new one!
      </Text>
      <Button onClick={handleTryAgain}>Try Again</Button>
    </div>
  )
}
