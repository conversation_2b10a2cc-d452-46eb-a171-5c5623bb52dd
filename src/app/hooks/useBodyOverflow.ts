import { useEffect } from "react"

/**
 * Custom hook to manage body overflow when modals, overlays, or drawers are open
 * @param open - Boolean indicating if the modal/overlay is open
 */
const useBodyOverflow = (open: boolean) => {
  useEffect(() => {
    if (open) {
      document.body.style.overflow = "hidden"
    }
    return () => {
      document.body.style.overflow = "auto"
    }
  }, [open])
}

export default useBodyOverflow
