import { useQuery } from "@tanstack/react-query"

import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { PlatformFeeApiRepository } from "@infrastructure/repositories/platformFeeApiRepository"

const useGetPlatformFee = ({
  memberId,
  categoryId,
}: {
  memberId?: number
  categoryId?: number
}) => {
  const PlatformFee = new PlatformFeeApiRepository()
  const query = useQuery({
    queryKey: [QueryKeysConstant.GET_PLATFORM_FEE, memberId],
    queryFn: () => {
      return PlatformFee.getMyPlatformFee()
    },
    enabled: Boolean(memberId),
  })

  const getMyPlatformFee = () => {
    if (!query.data) return undefined
    const findedPlatformFee = query.data.find(
      (fee) => fee.categoryId === categoryId,
    )
    return findedPlatformFee
  }

  return {
    getMyPlatformFee,
    ...query,
  }
}

export default useGetPlatformFee
