import { PopupBannerRepository } from "@domain/interfaces/PopupBannerRepository"
import { menuWizardApi } from "@infrastructure/api/menuWizardApi"
import { TApiResponse } from "types/apiResponse.type"

export class PopupBannerApiRepository implements PopupBannerRepository {
  async getCurrent(params: any): Promise<TApiResponse<any>> {
    const res = await menuWizardApi.getCurrent(params)
    return Promise.resolve(res as TApiResponse<any>)
  }
}
