import { createBaseHttpClient } from "@infrastructure/providers/httpClient"
import { convertToCamelCase } from "@utils/misc"
import { TApiResponse } from "types/apiResponse.type"

export const popupBannerApi = {
  getCurrent: async (params: any): Promise<TApiResponse<any>> => {
    const client = createBaseHttpClient({
      baseURL: process.env.NEXT_PUBLIC_API_BASE_CONSOLE_URL,
      useToken: false,
    })
    const response = await client.get(`/popup-banner/get-current`, {
      params,
    })
    return convertToCamelCase(response.data) as TApiResponse<any>
  },
}
